<!--
 * @Description: 选择学生弹框
 * @Autor: panmy
 * @Date: 2024-12-05 14:26:41
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-31 14:56:08
-->
<template>
  <BasicModal v-bind="$attrs" class="transfer-modal member-modal" @register="registerModal" title="学生列表" :width="1000">
    <BasicTable @register="registerTable"> </BasicTable>
    <template #footer>
      <a-button @click="handleChecked('selection')" type="primary">勾选添加</a-button>
      <a-button @click="handleChecked('all')">全部添加</a-button>
    </template>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { reactive, watch, ref, toRefs, unref, computed, nextTick, onMounted } from 'vue';

  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicTable, useTable, BasicColumn } from '@/components/Table';
  import { useMessage } from '@/hooks/web/useMessage';
  import { queryPersonnelPage } from '@/api/mtface/equipment';
  import * as schoolApi from '@/api/school';
  const { createMessage } = useMessage();
  const state = reactive({
    loading: false,
    dwdm: '',
    zydm: '',
    njdm: '',
    bjdm: '',
  });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(() => {
    loadXY();
  });

  const columns: BasicColumn[] = [
    { title: '学号', fixed: 'left', dataIndex: 'xh', width: 100 },
    { title: '姓名', fixed: 'left', dataIndex: 'xm', width: 80 },
    { title: '院系', dataIndex: 'dwbzmc', width: 150 },
    { title: '专业', dataIndex: 'zymc', width: 150 },
    { title: '现在年级', dataIndex: 'xznj', width: 80 },
    { title: '班级', dataIndex: 'bjmc', width: 100 },
    { title: '学生类别', dataIndex: 'xslb', width: 100 },
  ];

  const [registerTable, { reload, getForm, getSelectRowKeys }] = useTable({
    // api: studentAllList,
    maxHeight: 400,
    columns,
    immediate: true,
    useSearchForm: true,
    clickToRowSelect: true,
    rowSelection: {
      type: 'checkbox',
    },
    formConfig: {
      schemas: [
        {
          field: 'personName',
          label: '关键词',
          component: 'Input',
          componentProps: {
            placeholder: '请输入学号/姓名关键词',
            submitOnPressEnter: true,
          },
        },
        {
          field: 'dwdm',
          label: '学院',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            onChange: (val, obj) => {
              state.dwdm = val;
              state.zydm = '';
              state.njdm = '';
              state.bjdm = '';
              loadZY();
            },
          },
        },
        {
          field: 'zydm',
          label: '专业',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            onChange: (val, obj) => {
              state.zydm = val;
              state.njdm = '';
              state.bjdm = '';
              loadNJ();
            },
          },
        },
        {
          field: 'njdm',
          label: '年级',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
            onChange: (val, obj) => {
              state.njdm = val;
              state.bjdm = '';
              loadBJ();
            },
          },
        },
        {
          field: 'bjdm',
          label: '班级',
          component: 'Select',
          componentProps: {
            placeholder: '全部',
          },
        },
      ],
    },
    showTableSetting: false,
    tableSetting: { size: false, expand: false, redo: false, setting: false },
    canResize: true,
  });
  function handleChecked(operationType) {
    if (operationType == 'selection') {
      const ids = getSelectRowKeys();
      if (ids.length == 0) return createMessage.error('请先选择学生');
    }
  }

  // #region 学院、专业、年级、班级
  const loadXY = async type => {
    const res = await schoolApi.getXY({ pageSize: 99999 });
    getForm().updateSchema({
      field: 'dwdm',
      componentProps: {
        options: res.data.list,
        fieldNames: { label: 'dwBzmc', value: 'dwDm' },
      },
    });
    getForm().setFieldsValue({ dwdm: '', zydm: '', njdm: '', bjdm: '' });
    nextTick(() => {
      reload();
    });
  };
  const loadZY = () => {
    getForm().updateSchema({ field: 'zydm', componentProps: { options: [] } });
    getForm().updateSchema({ field: 'njdm', componentProps: { options: [] } });
    getForm().updateSchema({ field: 'bjdm', componentProps: { options: [] } });
    getForm().setFieldsValue({ zydm: '', njdm: '', bjdm: '' });
    schoolApi.getZY({ dwdm: state.dwdm, pageSize: 99999 }).then(res => {
      getForm().updateSchema({ field: 'zydm', componentProps: { options: res.data.list, fieldNames: { label: 'zyMc', value: 'zyDm' } } });
    });
  };
  const loadNJ = () => {
    getForm().updateSchema({ field: 'njdm', componentProps: { options: [] } });
    getForm().updateSchema({ field: 'bjdm', componentProps: { options: [] } });
    getForm().setFieldsValue({ njdm: '', bjdm: '' });
    schoolApi.getNJ({ dwdm: state.dwdm, zydm: state.zydm, pageSize: 99999 }).then(res => {
      const data = res.data.list.map(item => {
        return { label: item, value: item };
      });
      getForm().updateSchema({ field: 'njdm', componentProps: { options: data, fieldNames: { label: 'label', value: 'value' } } });
    });
  };

  const loadBJ = () => {
    schoolApi.getBJ({ dwdm: state.dwdm, zydm: state.zydm, nj: state.njdm }).then(res => {
      getForm().updateSchema({ field: 'bjdm', componentProps: { options: res.data.list, fieldNames: { label: 'bjMc', value: 'bjDm' } } });
    });
  };
  //#endregion
</script>
