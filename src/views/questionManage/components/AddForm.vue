<!--
 * @Description: 创建问卷
 * @Autor: Fhz
 * @Date: 2025-02-11 15:30:59
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-31 14:28:08
-->
<template>
  <a-row class="mb-100px">
    <a-col :span="14" :offset="5">
      <Spin :spinning="spinning">
        <BasicForm @register="registerForm">
          <template #startTime="{ model, field }">
            <range-picker v-model:value="model[field]" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
          </template>
        </BasicForm>
      </Spin>
    </a-col>
  </a-row>
</template>
<script lang="ts" setup>
  import { ref, onMounted, reactive, toRaw, nextTick } from 'vue';
  import { RangePicker, Spin } from 'ant-design-vue';
  import { BasicForm, useForm, FormSchema } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import dayjs from 'dayjs';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import * as applicationApi from '@/api/application/application';

  const api = useBaseApi('/api/knsDcwj');
  const emit = defineEmits(['register', 'reload']);
  const props = defineProps(['stepIds', 'stepItems', 'shareInfo']);
  const { createMessage } = useMessage();
  const id = ref('');
  const spinning = ref(false);
  const schemas: FormSchema[] = [
    {
      field: 'wjmc',
      label: '问卷名称',
      component: 'Input',
      rules: [{ required: true, message: '问卷名称不能为空' }],
    },
    {
      field: 'zf',
      label: '总分数',
      component: 'InputNumber',
      helpMessage: '问卷可得最大分值，存在使用分值题目有效',
      rules: [{ required: true, message: '总分数不能为空' }],
    },
    {
      field: 'txsm',
      label: '填写说明',
      component: 'Textarea',
      componentProps: {
        maxLength: 1000,
        placeholder: '请输入填写说明',
      },
    },
    {
      field: 'dateRange',
      label: '填写时间',
      component: 'Input',
      slot: 'startTime',
      componentProps: {
        placeholder: ['开始时间', '结束时间'],
      },
    },
    {
      field: 'syfw',
      label: '使用范围',
      component: 'Select',
      componentProps: {},
      rules: [{ required: true, message: '请选择使用范围' }],
    },
    {
      field: 'dcdx',
      label: '调查对象',
      component: 'Radio',
      componentProps: {
        options: [
          { fullName: '全体学生', id: '0' },
          { fullName: '部分学生', id: '1' },
        ],
      },
      rules: [{ required: true, message: '请选择使用范围' }],
    },
    {
      field: 'sfsy',
      label: '是否使用',
      component: 'Switch',
      defaultValue: true,
    },
    {
      field: 'dctx',
      label: '是否多次填写',
      component: 'Switch',
    },
  ];

  const [registerForm, { setFieldsValue, getFieldsValue, validate, resetFields, updateSchema }] = useForm({
    labelWidth: 100,
    schemas: schemas,
  });
  async function getDetail(data) {
    spinning.value = true;
    resetFields();
    id.value = props.stepIds[0] || '';
    await getOptions();
    if (id.value) {
      const { data } = await api.getDetail(toRaw(props.stepIds[0]));
      data.dateRange = [dayjs(data.txkssj), dayjs(data.txjssj)];
      setFieldsValue({
        ...data,
        zf: Number(data.zf || 0),
      });
    }
    spinning.value = false;
  }
  async function getOptions() {
    const {
      data: { list: sysList },
    } = await applicationApi.getSystemList({ currentPage: 1, pageSize: 99999 });
    updateSchema({
      field: 'syfw',
      componentProps: { options: sysList || [], fieldNames: { label: 'name', value: 'id' }, showSearch: true },
    });
  }

  async function save() {
    const values = await validate();
    if (!values) return;
    if (values.dateRange) {
      values.txkssj = dayjs(values.dateRange[0]).format('YYYY-MM-DD 00:00:00');
      values.txjssj = dayjs(values.dateRange[1]).format('YYYY-MM-DD 23:59:59');
    }
    const query = {
      ...values,
      id: id.value,
    };

    const res = id.value
      ? await api.edit({ data: query, requestOptions: { isTransformResponse: true } })
      : await api.save({ data: query, requestOptions: { isTransformResponse: true } });

    return {
      success: true,
      id: id.value || 'new-generated-id',
      errorMessage: '',
    };
  }

  defineExpose({
    save,
    getDetail,
  });
</script>
<style scoped lang="less">
  :deep(.ant-alert-with-description) {
    padding-block: 5px !important;
  }
  :deep(.ant-descriptions .ant-descriptions-item-label) {
    padding: 10px !important;
    width: 130px !important;
  }
</style>
